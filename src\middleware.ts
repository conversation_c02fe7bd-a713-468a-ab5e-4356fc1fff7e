import { createServer<PERSON><PERSON> } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { UserRole } from '@/types/roles';
import { updateSession } from '@/utils/supabase/middleware';
import { isEventRegistrationUrl, fixCrossEnvironmentRedirect, getBaseUrl } from '@/utils/url-utilities';

/**
 * Middleware for handling authentication and authorization with Supabase Auth
 *
 * This middleware:
 * 1. Updates the session using the Supabase middleware helper
 * 2. Checks if the user is authenticated
 * 3. Redirects unauthenticated users to the sign-in page for protected routes
 * 4. Checks user roles for admin routes
 * 5. Adds user context to headers for server components
 */
export async function middleware(req: NextRequest) {
  // Check for excessive redirect count first, before doing anything else
  const initialRedirectCount = parseInt(req.headers.get('x-redirect-count') || '0');
  if (initialRedirectCount > 5) {
    console.error('Excessive redirect count detected, breaking the cycle and returning to homepage');
    return NextResponse.redirect(new URL('/', req.url));
  }

  // First, update the session using the Supabase middleware helper
  // This ensures cookies are properly handled and the session is refreshed
  const res = await updateSession(req);

  // Get the URL and path
  const url = new URL(req.url);
  const path = url.pathname;

  // Set dynamic port for URL handling
  if (typeof global !== 'undefined') {
    console.log(`Middleware detected URL: ${req.url} with port: ${url.port || 'none'}`);

    if (url.port) {
      // @ts-ignore - This is a custom property we're adding to the global object
      global.__NEXT_DYNAMIC_PORT = url.port;
      // Also set it as an environment variable for other parts of the app
      process.env.NEXT_PUBLIC_DYNAMIC_PORT = url.port;
      console.log(`Set dynamic port to: ${url.port}`);
    }
  }

  // Create a Supabase client for the middleware
  // We need to create a new client here to use the updated cookies from updateSession
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          // This is handled by updateSession
        },
        remove(name: string, options: any) {
          // This is handled by updateSession
        },
      },
    }
  );

  // Check if the user is authenticated - use getUser() for security
  // getUser() is more secure than getSession() as it validates the JWT
  const { data: { user }, error } = await supabase.auth.getUser();

  // Also get the session to ensure we have a valid session
  const { data: { session } } = await supabase.auth.getSession();

  // User is authenticated if we have both a user and a session, and no error
  const isAuthenticated = !!user && !!session && !error;

  // Log authentication state in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Middleware auth check for ${req.url}:`, {
      hasUser: !!user,
      hasSession: !!session,
      hasError: !!error,
      isAuthenticated
    });
  }

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/about(.*)",
    "/contact(.*)",
    "/api/webhooks/supabase",
    "/sign-in(.*)",
    "/sign-up(.*)",
    "/_next/(.*)",
    "/favicon.ico",
    "/api/webhooks(.*)",
    "/api/health",
    "/reset(.*)",
    "/events/:path*",  // Public event pages
    "/api/debug-session",
    "/api/auth/(.*)",
    "/auth/(.*)",
    "/api/admin/promote",
    "/api/upload(.*)",  // Add upload API to public routes for debugging
    "/api/upload/test",  // Add test API to public routes
    "/api/ping"         // Simple ping API for testing
  ];

  // Event registration pages require authentication but should redirect back after sign-in
  const eventRegistrationRoutes = [
    "/events/:slug/register(.*)"
  ];

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route.includes('*')) {
      const baseRoute = route.replace('(.*)', '').replace(':path*', '');
      return path.startsWith(baseRoute);
    }
    return path === route;
  });

  // Check if the current path is the sign-in page
  const isSignInPage = path === '/sign-in';

  // Check for OAuth errors in the URL (this happens when Supabase redirects with errors)
  const reqUrl = new URL(req.url);
  const oauthError = reqUrl.searchParams.get('error');
  const oauthErrorCode = reqUrl.searchParams.get('error_code');

  // If we have an OAuth error in the root URL, redirect to sign-in with the error
  if (path === '/' && oauthError && (oauthErrorCode === 'bad_oauth_state' || oauthError === 'invalid_request')) {
    console.log('Detected OAuth error in root URL, redirecting to sign-in');
    const signInUrl = new URL('/sign-in', req.url);

    // Copy all error parameters to the sign-in URL
    reqUrl.searchParams.forEach((value, key) => {
      if (key.startsWith('error')) {
        signInUrl.searchParams.set(key, value);
      }
    });

    // Add reset_auth parameter to trigger a fresh authentication flow
    signInUrl.searchParams.set('reset_auth', 'true');

    return NextResponse.redirect(signInUrl);
  }

  // Check if this is a background refresh request
  const isBackgroundRefresh = req.headers.get('x-background-refresh') === 'true' ||
    req.headers.get('x-middleware-prefetch') === '1';

  // If this is a background refresh or we have a high redirect count, pass through without redirecting
  if (isBackgroundRefresh || initialRedirectCount > 2) {
    console.log('Background refresh or high redirect count detected, passing through request without redirecting');
    return res;
  }

  // If the user is authenticated and trying to access the sign-in page, redirect to dashboard
  if (isAuthenticated && isSignInPage) {
    console.log('Authenticated user trying to access sign-in page, redirecting to dashboard');

    // Check if there's a redirect_url parameter in the sign-in URL
    const redirectUrl = url.searchParams.get('redirect_url');

    if (redirectUrl) {
      // If there's a redirect URL, use it instead of the dashboard
      console.log('Found redirect_url parameter, redirecting to:', redirectUrl);

      // Fix any cross-environment redirects
      const fixedRedirectUrl = fixCrossEnvironmentRedirect(
        redirectUrl.startsWith('http') ? redirectUrl : `${url.protocol}//${url.host}${redirectUrl}`,
        req.url
      );
      console.log('Fixed redirect URL for authenticated user:', fixedRedirectUrl);

      // Create a URL object for the redirect
      const redirectUrlObj = new URL(fixedRedirectUrl);

      // Add a timestamp to prevent caching issues
      redirectUrlObj.searchParams.set('_ts', Date.now().toString());

      return NextResponse.redirect(redirectUrlObj);
    } else {
      // Create a redirect to dashboard
      const dashboardUrl = new URL('/dashboard', req.url);

      return NextResponse.redirect(dashboardUrl);
    }
  }

  // If the user is not authenticated and the route is not public, redirect to sign-in
  if (!isAuthenticated && !isPublicRoute) {
    // Create a sign-in URL with the current URL as the redirect target
    const signInUrl = new URL('/sign-in', req.url);

    // Only add redirect_url if this is not an auth callback or sign-in page
    // to prevent potential redirect loops
    if (!req.url.includes('/auth/callback') && !req.url.includes('/sign-in')) {
      // Store the original URL as the redirect target
      const currentUrl = new URL(req.url);

      // Create a clean URL for redirection after sign-in

      // Fix any cross-environment redirects
      const fixedRedirectUrl = fixCrossEnvironmentRedirect(currentUrl.toString(), req.url);
      console.log('Fixed redirect URL for auth:', fixedRedirectUrl);

      // Set the modified URL as the redirect_url
      signInUrl.searchParams.set('redirect_url', fixedRedirectUrl);
    }

    // Check if this is an event registration URL and log it for debugging
    if (isEventRegistrationUrl(req.url)) {
      console.log('Middleware detected event registration URL:', req.url);
    }

    // Add a redirect count header to the request to track potential loops
    const redirectCount = initialRedirectCount + 1;
    const requestHeaders = new Headers(req.headers);
    requestHeaders.set('x-redirect-count', redirectCount.toString());

    // Create a new response with the updated headers
    const redirectResponse = NextResponse.redirect(signInUrl);

    // Copy the redirect count header to the response
    redirectResponse.headers.set('x-redirect-count', redirectCount.toString());

    return redirectResponse;
  }

  // For admin routes, check if user has admin role
  if (path.startsWith('/admin') && isAuthenticated) {
    try {
      // Get user role from database
      // First try with auth_user_id if it exists
      let userData = null;
      const { data: userDataByAuthId, error: authIdError } = await supabase
        .from('users')
        .select('role')
        .eq('auth_user_id', user.id)
        .maybeSingle();

      if (userDataByAuthId) {
        userData = userDataByAuthId;
      } else {
        // Fallback to using the email as identifier
        const { data: userDataByEmail } = await supabase
          .from('users')
          .select('role')
          .eq('email', user.email)
          .maybeSingle();

        userData = userDataByEmail;
      }

      if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.SUPER_ADMIN)) {
        // Redirect non-admin users to dashboard
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }
    } catch (error) {
      console.error('Error checking admin role:', error);
      // If there's an error checking the role, redirect to dashboard
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }
  }

  // Add user context to headers for server components
  // This allows server components to access the user ID without making additional database queries
  if (user) {
    const requestHeaders = new Headers(req.headers);
    requestHeaders.set('x-user-id', user.id);

    // Add user email for debugging and server-side personalization
    if (user.email) {
      requestHeaders.set('x-user-email', user.email);
    }

    // Add user role if available in user metadata
    if (user.user_metadata?.role) {
      requestHeaders.set('x-user-role', user.user_metadata.role);
    }

    // Create a new response with the updated headers
    const newResponse = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    // Copy cookies from the original response
    // This is critical to maintain the authentication state
    res.cookies.getAll().forEach(cookie => {
      newResponse.cookies.set(cookie.name, cookie.value, cookie);
    });

    // Add security headers to the response
    newResponse.headers.set('X-Frame-Options', 'DENY');
    newResponse.headers.set('X-Content-Type-Options', 'nosniff');
    newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    newResponse.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()');

    return newResponse;
  }

  return res;
}

// Matcher configuration for the middleware
// This ensures the middleware only runs on relevant paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files with specific extensions
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
    // Explicitly include API routes
    '/api/:path*',
  ],
};