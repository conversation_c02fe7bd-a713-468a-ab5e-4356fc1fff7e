'use client';

import { useState } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { TimePicker } from '@/components/ui/time-picker';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, X, Edit2, Info, Copy, Trash } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { z } from 'zod';
import { toast } from '@/components/ui/use-toast';
import { getEventTypeById } from '@/app/actions/events';
import { useEffect } from 'react';
import { formatCurrency } from '@/lib/utils/currency-utils';
import {
  baseEventCategorySchema,
  runningEventCategoryPropertiesSchema,
  conferenceEventCategoryPropertiesSchema
} from '@/types/event-types';
import {
  EVENT_TYPE_TEMPLATES,
  getCategoryTemplateForEventType
} from '@/lib/event-type-templates';

// JSON-based category validation schema
const categorySchema = baseEventCategorySchema.extend({
  eventType: z.string().optional(),
});

export function CategoriesStep() {
  const { formData, updateFormData, nextStep } = useWizard();

  // Log formData categories for debugging
  console.log('[DEBUG] CategoriesStep - formData.categories:', formData.categories);

  // If categories exist, log their price information
  if (Array.isArray(formData.categories)) {
    formData.categories.forEach((category: any, index: number) => {
      console.log(`[DEBUG] CategoriesStep - Category ${index + 1} (${category.name}) price info:`, {
        directPrice: category.price,
        propertiesPrice: category.properties?.price,
        propertiesObject: category.properties
      });
    });
  }

  const [categories, setCategories] = useState<Array<{
    name: string;
    description?: string;
    properties: Record<string, any>;
    eventType?: string;
  }>>(formData.categories || []);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [eventType, setEventType] = useState<string>('');
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [allowCategorySpecificClosingDates, setAllowCategorySpecificClosingDates] = useState(
    formData.allowCategorySpecificClosingDates || false
  );

  // Initialize with empty form and ensure all values are defined
  const [currentCategory, setCurrentCategory] = useState<{
    name: string;
    description: string;
    properties: Record<string, any>;
  }>({
    name: '',
    description: '',
    // JSON-based properties
    properties: {
      price: '',
      startTime: '',
      earlyBirdPrice: '',
      earlyBirdEndDate: '',
      bibPrefix: '',
      bibStartNumber: '',
      bibRequireGeneration: true,
      registrationOpen: true,
      registrationCloseDate: '',
      registrationLimit: '',
      registrationCount: 0,
      // Ensure all possible properties have default values
      distance: '',
      elevation: '',
      terrain: '',
      ageGroup: '',
      sessionType: '',
      speakerName: '',
      speakerBio: '',
      venueDetails: '',
    },
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isRunEvent, setIsRunEvent] = useState(false);

  // Check event type and initialize properties based on event type
  useEffect(() => {
    const checkEventType = async () => {
      if (formData.eventTypeId) {
        const response = await getEventTypeById(formData.eventTypeId);
        if (response.success && response.data) {
          const slug = response.data.slug;
          setIsRunEvent(slug === 'runs');
          setEventType(slug);

          // Initialize properties based on event type template
          const template = getCategoryTemplateForEventType(slug);

          // Create a safe copy of template properties
          const safeTemplateProps: Record<string, any> = { ...template.properties };

          // Ensure all template properties have safe values
          Object.keys(safeTemplateProps).forEach(key => {
            // Skip boolean properties
            if (key === 'bibRequireGeneration' || key === 'registrationOpen') {
              safeTemplateProps[key] = safeTemplateProps[key] ?? true;
            }
            // Skip numeric properties that should remain numbers
            else if (key === 'registrationCount') {
              safeTemplateProps[key] = safeTemplateProps[key] ?? 0;
            }
            // Convert all other properties to strings with empty fallback
            else if (safeTemplateProps[key] === undefined || safeTemplateProps[key] === null) {
              safeTemplateProps[key] = '';
            }
          });

          // Update current category with safe template properties
          setCurrentCategory(prev => ({
            ...prev,
            properties: {
              ...prev.properties,
              ...safeTemplateProps,
              // Ensure these specific properties are always defined
              price: safeTemplateProps.price || '',
              startTime: safeTemplateProps.startTime || '',
              earlyBirdPrice: safeTemplateProps.earlyBirdPrice || '',
              earlyBirdEndDate: safeTemplateProps.earlyBirdEndDate || '',
              bibPrefix: safeTemplateProps.bibPrefix || '',
              bibStartNumber: safeTemplateProps.bibStartNumber || '',
              registrationCloseDate: safeTemplateProps.registrationCloseDate || '',
              registrationLimit: safeTemplateProps.registrationLimit || ''
            }
          }));
        }
      }
    };

    checkEventType();
  }, [formData.eventTypeId]);

  // Add autofocus to first input when component mounts
  useEffect(() => {
    const nameInput = document.getElementById('name') as HTMLInputElement;
    if (nameInput) {
      nameInput.focus();
    }
  }, []);

  // Handle input change for category form
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // Always use empty string instead of undefined or null
    const safeValue = value ?? '';

    // Handle base fields
    if (name === 'name' || name === 'description') {
      setCurrentCategory(prev => ({ ...prev, [name]: safeValue }));
    }
    // Handle properties fields
    else {
      setCurrentCategory(prev => ({
        ...prev,
        properties: {
          ...prev.properties,
          [name]: safeValue
        }
      }));
    }

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle switch toggle
  const handleSwitchChange = (name: string) => (checked: boolean) => {
    setCurrentCategory(prev => ({
      ...prev,
      properties: {
        ...prev.properties,
        [name]: checked
      }
    }));
  };

  // Add or update category
  const addOrUpdateCategory = () => {
    try {
      // Get properties from the form
      const properties: Record<string, any> = { ...currentCategory.properties };

      // Convert string values to appropriate types
      if (properties.price) {
        console.log('[DEBUG] Converting price:', {
          originalPrice: properties.price,
          originalType: typeof properties.price,
          parsedPrice: parseFloat(properties.price),
          parsedType: typeof parseFloat(properties.price)
        });
        properties.price = parseFloat(properties.price);
      }

      if (properties.earlyBirdPrice) {
        console.log('[DEBUG] Converting earlyBirdPrice:', {
          originalPrice: properties.earlyBirdPrice,
          originalType: typeof properties.earlyBirdPrice,
          parsedPrice: parseFloat(properties.earlyBirdPrice),
          parsedType: typeof parseFloat(properties.earlyBirdPrice)
        });
        properties.earlyBirdPrice = parseFloat(properties.earlyBirdPrice);
      }
      if (properties.registrationLimit) {
        // Store the original value for comparison
        const originalValue = properties.registrationLimit;
        console.log('Registration limit original value:', originalValue, 'type:', typeof originalValue);

        // IMPORTANT: Store the value exactly as entered, without any conversion
        // This preserves the exact number the user entered
        properties.registrationLimit = originalValue;

        console.log('Registration limit final value (no conversion):', properties.registrationLimit);
      }

      // Handle time values - ensure they're stored as time strings (HH:MM format)
      if (properties.startTime) {
        // Make sure it's just the time part (HH:MM)
        const timeMatch = properties.startTime.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
          properties.startTime = timeMatch[0];
        }
      }

      // Ensure registrationCount is initialized
      if (properties.registrationCount === undefined) {
        properties.registrationCount = 0;
      }

      // Create the category object with properties
      const categoryToSave = {
        name: currentCategory.name,
        description: currentCategory.description,
        properties,
        eventType
      };

      // Validate with the JSON schema
      categorySchema.parse(categoryToSave);

      let updatedCategories;

      if (editIndex !== null) {
        // Update existing category
        updatedCategories = [...categories];
        updatedCategories[editIndex] = categoryToSave;
      } else {
        // Add new category
        updatedCategories = [...categories, categoryToSave];
      }

      // Update local state
      setCategories(updatedCategories);

      // Update form data with explicit new array
      updateFormData({ categories: updatedCategories });

      console.log('[DEBUG] Categories updated:', JSON.stringify(updatedCategories, null, 2));

      // Log detailed information about each category's price
      updatedCategories.forEach((category, idx) => {
        console.log(`[DEBUG] Category ${idx + 1} (${category.name}) after update:`, {
          directPrice: category.properties?.price,
          propertiesPrice: category.properties?.price,
          propertiesType: typeof category.properties?.price,
          hasProperties: !!category.properties,
          propertiesKeys: category.properties ? Object.keys(category.properties) : []
        });
      });

      // Get template for current event type
      const template = getCategoryTemplateForEventType(eventType);

      // Reset form with safe default values
      const templateProps: Record<string, any> = { ...template.properties };

      // Ensure all template properties have safe values
      Object.keys(templateProps).forEach(key => {
        // Skip boolean properties
        if (key === 'bibRequireGeneration' || key === 'registrationOpen') {
          templateProps[key] = templateProps[key] ?? true;
        }
        // Skip numeric properties that should remain numbers
        else if (key === 'registrationCount') {
          templateProps[key] = templateProps[key] ?? 0;
        }
        // Convert all other properties to strings with empty fallback
        else if (templateProps[key] === undefined || templateProps[key] === null) {
          templateProps[key] = '';
        }
      });

      setCurrentCategory({
        name: '',
        description: '',
        properties: {
          ...templateProps,
          price: '',
          startTime: '',
          earlyBirdPrice: '',
          earlyBirdEndDate: '',
          bibPrefix: '',
          bibStartNumber: '',
          bibRequireGeneration: true,
          registrationOpen: true,
          registrationCloseDate: '',
          registrationLimit: '',
          registrationCount: 0,
          // Ensure all possible properties have default values
          distance: '',
          elevation: '',
          terrain: '',
          ageGroup: '',
          sessionType: '',
          speakerName: '',
          speakerBio: '',
          venueDetails: ''
        }
      });
      setEditIndex(null);
      setValidationErrors({});

      // Scroll to the Categories list heading after updating
      setTimeout(() => {
        const categoriesListHeading = document.querySelector('[data-categories-list-heading]') as HTMLElement;
        if (categoriesListHeading) {
          categoriesListHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    } catch (error) {
      console.error('Category validation error:', error);

      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            if (err.path && err.path.length > 0 && err.path[0]) {
              errors[err.path[0]] = err.message;
            }
          }
        });
        setValidationErrors(errors);

        toast({
          title: "Validation Error",
          description: "Please fix the errors in the category form",
          variant: "destructive",
        });
      }
    }
  };

  // Edit category
  const editCategory = (index: number) => {
    if (index < 0 || index >= categories.length) {
      console.error('Invalid category index:', index);
      return;
    }

    const category = categories[index];
    if (!category) {
      console.error('Category not found at index:', index);
      return;
    }

    // Get properties (or initialize empty object)
    const props = category.properties || {};

    // Create a safe copy of all properties with string defaults
    const safeProps = { ...props };

    // Scroll to the Edit Category heading
    setTimeout(() => {
      const editCategoryHeading = document.querySelector('[data-edit-category-heading]') as HTMLElement;
      if (editCategoryHeading) {
        editCategoryHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);

    // Ensure all properties have string values (except booleans and numbers that should remain as is)
    Object.keys(safeProps).forEach(key => {
      // Skip boolean properties
      if (key === 'bibRequireGeneration' || key === 'registrationOpen') {
        safeProps[key] = safeProps[key] ?? true;
      }
      // Skip numeric properties that should remain numbers
      else if (key === 'registrationCount') {
        safeProps[key] = safeProps[key] ?? 0;
      }
      // Convert all other properties to strings with empty fallback
      else if (safeProps[key] === undefined || safeProps[key] === null) {
        safeProps[key] = '';
      }
      else if (typeof safeProps[key] !== 'string' && typeof safeProps[key] !== 'boolean' && typeof safeProps[key] !== 'number') {
        safeProps[key] = String(safeProps[key]) || '';
      }
    });

    // Get price from properties object
    const priceValue = props.price?.toString() || '';

    console.log('[DEBUG] Editing category:', {
      index,
      categoryObject: JSON.stringify(category),
      name: category?.name || '',
      directPrice: category?.properties?.price,
      propertiesObject: JSON.stringify(props),
      propertiesPrice: props.price,
      finalPrice: priceValue,
      hasProperties: !!category?.properties,
      propertiesKeys: category?.properties ? Object.keys(category.properties) : [],
      priceType: typeof props.price
    });

    // Set form values with safe properties
    setCurrentCategory({
      name: category?.name || '',
      description: category?.description || '',
      // Store the properties object with defaults for missing values
      properties: {
        price: priceValue,
        startTime: props.startTime || '',
        earlyBirdPrice: props.earlyBirdPrice?.toString() || '',
        earlyBirdEndDate: props.earlyBirdEndDate || '',
        bibPrefix: props.bibPrefix || '',
        bibStartNumber: props.bibStartNumber?.toString() || '',
        bibRequireGeneration: props.bibRequireGeneration ?? true,
        registrationOpen: props.registrationOpen ?? true,
        registrationCloseDate: props.registrationCloseDate || '',
        registrationLimit: props.registrationLimit?.toString() || '',
        registrationCount: props.registrationCount ?? 0,
        // Include all other properties with safe values
        ...safeProps
      }
    });

    setEditIndex(index);
  };

  // Duplicate category
  const duplicateCategory = (index: number) => {
    try {
      const categoryToDuplicate = categories[index];

      // Create a deep copy of the category
      const duplicatedCategory = JSON.parse(JSON.stringify(categoryToDuplicate));

      // Modify the name to indicate it's a copy
      duplicatedCategory.name = `${duplicatedCategory.name} (Copy)`;

      // Add the duplicated category to the list
      const updatedCategories = [...categories, duplicatedCategory];

      // Update local state
      setCategories(updatedCategories);

      // Update form data
      updateFormData({ categories: updatedCategories });

      toast({
        title: "Category Duplicated",
        description: "A copy of the category has been created. You can now edit it.",
      });
    } catch (error) {
      console.error('Error duplicating category:', error);
      toast({
        title: "Error",
        description: "Failed to duplicate category",
        variant: "destructive",
      });
    }
  };

  // Confirm delete dialog
  const confirmDelete = (index: number) => {
    setDeleteIndex(index);
    setShowDeleteDialog(true);
  };

  // Delete category
  const deleteCategory = () => {
    if (deleteIndex === null) return;

    try {
      const index = deleteIndex;
      const updatedCategories = [...categories];
      updatedCategories.splice(index, 1);

      // Update local state
      setCategories(updatedCategories);

      // Update form data with explicit new array
      updateFormData({ categories: updatedCategories });

      console.log('Category deleted, remaining categories:', updatedCategories);

      // If editing the deleted category, reset form
      if (editIndex === index) {
        // Get template for current event type
        const template = getCategoryTemplateForEventType(eventType);

        // Create safe template properties
        const templateProps: Record<string, any> = { ...template.properties };

        // Ensure all template properties have safe values
        Object.keys(templateProps).forEach(key => {
          // Skip boolean properties
          if (key === 'bibRequireGeneration' || key === 'registrationOpen') {
            templateProps[key] = templateProps[key] ?? true;
          }
          // Skip numeric properties that should remain numbers
          else if (key === 'registrationCount') {
            templateProps[key] = templateProps[key] ?? 0;
          }
          // Convert all other properties to strings with empty fallback
          else if (templateProps[key] === undefined || templateProps[key] === null) {
            templateProps[key] = '';
          }
        });

        setCurrentCategory({
          name: '',
          description: '',
          properties: {
            ...templateProps,
            price: '',
            startTime: '',
            earlyBirdPrice: '',
            earlyBirdEndDate: '',
            bibPrefix: '',
            bibStartNumber: '',
            bibRequireGeneration: true,
            registrationOpen: true,
            registrationCloseDate: '',
            registrationLimit: '',
            registrationCount: 0,
            // Ensure all possible properties have default values
            distance: '',
            elevation: '',
            terrain: '',
            ageGroup: '',
            sessionType: '',
            speakerName: '',
            speakerBio: '',
            venueDetails: ''
          }
        });
        setEditIndex(null);
      } else if (editIndex !== null && editIndex > index) {
        // Adjust edit index if we're editing a category after the deleted one
        setEditIndex(editIndex - 1);
      }

      // Reset delete state
      setDeleteIndex(null);
      setShowDeleteDialog(false);

      toast({
        title: "Category Deleted",
        description: "The category has been removed from the event",
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: "Error",
        description: "Failed to delete category",
        variant: "destructive",
      });
    }
  };

  // Validate and proceed to next step
  const validateAndProceed = () => {
    if (categories.length === 0) {
      toast({
        title: "No Categories",
        description: isRunEvent
          ? "Running events require at least one category"
          : "Consider adding at least one category for your event",
        variant: isRunEvent ? "destructive" : "default",
      });

      if (!isRunEvent) {
        nextStep();
      }
    } else {
      nextStep();
    }
  };

  // Override next button to include validation
  const originalNextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
  if (originalNextButton) {
    originalNextButton.onclick = validateAndProceed;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Event Categories</h2>
        <p className="text-gray-500">
          Define different categories for your event (e.g., age groups, distances, ticket types)
        </p>
      </div>

      {/* Category Form */}
      <Card>
        <CardHeader>
          <CardTitle data-edit-category-heading>{editIndex !== null ? 'Edit Category' : 'Add Category'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Category Name
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={currentCategory.name}
                  onChange={handleChange}
                  placeholder="e.g., Adult, 5K Run, VIP"
                  className={validationErrors.name ? "border-red-500" : ""}
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-500">{validationErrors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="registrationLimit">
                  Registration Limit
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 ml-1 inline-block text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        Maximum number of registrations for this category
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="registrationLimit"
                  name="registrationLimit"
                  type="number"
                  value={currentCategory.properties.registrationLimit || ''}
                  onChange={handleChange}
                  placeholder="Maximum registrations"
                  className={validationErrors.registrationLimit ? "border-red-500" : ""}
                />
                {validationErrors.registrationLimit && (
                  <p className="text-sm text-red-500">{validationErrors.registrationLimit}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={currentCategory.description}
                onChange={handleChange}
                placeholder="Describe this category"
                rows={2}
                className={validationErrors.description ? "border-red-500" : ""}
              />
              {validationErrors.description && (
                <p className="text-sm text-red-500">{validationErrors.description}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">
                  Price
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 ml-1 inline-block text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        Regular registration price
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  step="0.01"
                  value={currentCategory.properties.price || ''}
                  onChange={handleChange}
                  placeholder="0.00"
                  className={validationErrors.price ? "border-red-500" : ""}
                />
                {validationErrors.price && (
                  <p className="text-sm text-red-500">{validationErrors.price}</p>
                )}
              </div>

              <TimePicker
                id="startTime"
                name="startTime"
                label="Start Time"
                value={currentCategory.properties.startTime || ''}
                onChange={handleChange}
                error={validationErrors.startTime}
              />
            </div>

            {/* Early Bird Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="earlyBirdPrice">Early Bird Price</Label>
                <Input
                  id="earlyBirdPrice"
                  name="earlyBirdPrice"
                  type="number"
                  step="0.01"
                  value={currentCategory.properties.earlyBirdPrice || ''}
                  onChange={handleChange}
                  placeholder="0.00"
                  className={validationErrors.earlyBirdPrice ? "border-red-500" : ""}
                />
                {validationErrors.earlyBirdPrice && (
                  <p className="text-sm text-red-500">{validationErrors.earlyBirdPrice}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="earlyBirdEndDate">Early Bird End Date</Label>
                <Input
                  id="earlyBirdEndDate"
                  name="earlyBirdEndDate"
                  type="datetime-local"
                  value={currentCategory.properties.earlyBirdEndDate || ''}
                  onChange={handleChange}
                  className={validationErrors.earlyBirdEndDate ? "border-red-500" : ""}
                />
                {validationErrors.earlyBirdEndDate && (
                  <p className="text-sm text-red-500">{validationErrors.earlyBirdEndDate}</p>
                )}
              </div>
            </div>

            {/* Running-specific fields */}
            {isRunEvent && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bibPrefix">
                    Bib Number Prefix
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 inline-block text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          Prefix for bib numbers (e.g., "5K-" will make bib numbers like "5K-101")
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Input
                    id="bibPrefix"
                    name="bibPrefix"
                    value={currentCategory.properties.bibPrefix || ''}
                    onChange={handleChange}
                    placeholder="e.g., 5K-"
                    className={validationErrors.bibPrefix ? "border-red-500" : ""}
                  />
                  {validationErrors.bibPrefix && (
                    <p className="text-sm text-red-500">{validationErrors.bibPrefix}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bibStartNumber">
                    Starting Bib Number
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 inline-block text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          First bib number to assign (e.g., 101)
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Input
                    id="bibStartNumber"
                    name="bibStartNumber"
                    type="text"
                    value={currentCategory.properties.bibStartNumber || ''}
                    onChange={handleChange}
                    placeholder="e.g., 101"
                    className={validationErrors.bibStartNumber ? "border-red-500" : ""}
                  />
                  {validationErrors.bibStartNumber && (
                    <p className="text-sm text-red-500">{validationErrors.bibStartNumber}</p>
                  )}
                </div>
              </div>
            )}

            {/* Registration settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="registrationCloseDate">Registration Close Date</Label>
                <Input
                  id="registrationCloseDate"
                  name="registrationCloseDate"
                  type="datetime-local"
                  value={currentCategory.properties.registrationCloseDate || ''}
                  onChange={handleChange}
                  className={validationErrors.registrationCloseDate ? "border-red-500" : ""}
                  disabled={!formData.allowCategorySpecificClosingDates}
                />
                {!formData.allowCategorySpecificClosingDates && (
                  <p className="text-xs text-gray-500">
                    Enable category-specific closing dates in the Basic Details step to edit this field.
                  </p>
                )}
                {formData.allowCategorySpecificClosingDates && (
                  <p className="text-xs text-green-500">
                    Category-specific closing dates are enabled. Each category can have its own registration deadline.
                  </p>
                )}
                {validationErrors.registrationCloseDate && (
                  <p className="text-sm text-red-500">{validationErrors.registrationCloseDate}</p>
                )}
              </div>

              <div className="space-y-2 flex items-center">
                <div className="flex items-center space-x-2 pt-8">
                  <Switch
                    id="registrationOpen"
                    checked={currentCategory.properties.registrationOpen}
                    onCheckedChange={handleSwitchChange('registrationOpen')}
                  />
                  <Label htmlFor="registrationOpen">Registration is open</Label>
                </div>
              </div>
            </div>



            {isRunEvent && (
              <div className="flex items-center space-x-2">
                <Switch
                  id="bibRequireGeneration"
                  checked={currentCategory.properties.bibRequireGeneration}
                  onCheckedChange={handleSwitchChange('bibRequireGeneration')}
                />
                <Label htmlFor="bibRequireGeneration">Generate bib numbers automatically</Label>
              </div>
            )}



            <Button
              type="button"
              onClick={addOrUpdateCategory}
              className="mt-4"
            >
              {editIndex !== null ? 'Update Category' : 'Add Category'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Categories List */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium" data-categories-list-heading>Categories ({categories.length})</h3>

        {categories.length > 0 ? (
          <div className="space-y-3">
            {categories.map((category, index) => (
              <Card key={index} className="relative">
                <CardContent className="pt-6">
                  <div className="absolute top-2 right-2 flex space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => editCategory(index)}
                      className="h-8 w-8"
                      title="Edit category"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => duplicateCategory(index)}
                      className="h-8 w-8"
                      title="Duplicate category"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => confirmDelete(index)}
                      className="h-8 w-8 text-red-500 hover:text-red-600"
                      title="Delete category"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold">{category.name}</h4>
                      {/* Show price from properties with correct currency */}
                      {category.properties?.price && (
                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                          {formatCurrency(Number(category.properties.price), formData.country)}
                        </span>
                      )}
                    </div>

                    {category.description && (
                      <p className="text-sm text-gray-500">{category.description}</p>
                    )}

                    {/* Display category properties */}
                    <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                      {/* Get properties from properties object */}
                      {category.properties?.registrationLimit && (
                        <div>
                          <span className="font-medium">Reg. Limit:</span> {category.properties.registrationLimit}
                        </div>
                      )}
                      {category.properties?.startTime && (
                        <div>
                          <span className="font-medium">Start Time:</span> {category.properties.startTime}
                        </div>
                      )}
                      {isRunEvent && category.properties?.distance && (
                        <div>
                          <span className="font-medium">Distance:</span> {category.properties.distance} km
                        </div>
                      )}
                      {isRunEvent && category.properties?.bibPrefix && (
                        <div>
                          <span className="font-medium">Bib Prefix:</span> {category.properties.bibPrefix}
                        </div>
                      )}
                    </div>


                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="border rounded-md p-8 text-center text-gray-500">
            <p>No categories defined yet. Add your first category to get started.</p>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the category. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteIndex(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteCategory} className="bg-red-500 hover:bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}